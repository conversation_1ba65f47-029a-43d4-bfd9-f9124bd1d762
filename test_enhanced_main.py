# coding:utf-8
# test_enhanced_main.py - 测试增强版主程序的内容获取功能

import sys
import os

def test_import():
    """测试导入是否正常"""
    try:
        from main_enhanced import basic_crawler, show_menu
        from enhanced_wx_crawler import EnhancedWxCrawler
        print("✅ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_enhanced_crawler_init():
    """测试EnhancedWxCrawler初始化"""
    try:
        from enhanced_wx_crawler import EnhancedWxCrawler
        
        # 测试不同的初始化参数
        crawler1 = EnhancedWxCrawler("token", "biz", "cookie", get_content=False)
        print("✅ 创建仅链接模式爬虫成功")
        
        crawler2 = EnhancedWxCrawler("token", "biz", "cookie", get_content=True)
        print("✅ 创建内容获取模式爬虫成功")
        
        # 检查参数设置
        assert crawler1.get_content == False
        assert crawler2.get_content == True
        print("✅ 参数设置正确")
        
        return True
    except Exception as e:
        print(f"❌ EnhancedWxCrawler初始化测试失败: {e}")
        return False

def test_menu_display():
    """测试菜单显示"""
    try:
        from main_enhanced import show_menu
        print("📋 测试菜单显示:")
        show_menu()
        print("✅ 菜单显示正常")
        return True
    except Exception as e:
        print(f"❌ 菜单显示测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("🧪 开始测试增强版主程序...")
    print("=" * 50)
    
    tests = [
        ("模块导入测试", test_import),
        ("EnhancedWxCrawler初始化测试", test_enhanced_crawler_init),
        ("菜单显示测试", test_menu_display)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强版主程序功能正常")
        print("\n📖 使用说明:")
        print("1. 运行 python main_enhanced.py")
        print("2. 选择选项 2 - 基础文章抓取")
        print("3. 在内容获取选项中选择:")
        print("   - 选项1: 仅获取文章链接（快速）")
        print("   - 选项2: 同时获取文章完整内容（较慢）")
        print("4. 数据将保存到Excel和JSON文件中")
    else:
        print("❌ 部分测试失败，请检查代码")

if __name__ == '__main__':
    main()

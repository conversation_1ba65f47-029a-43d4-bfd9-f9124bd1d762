# coding:utf-8
# main_enhanced.py
"""
微信公众号爬虫主程序 - 增强版
整合了文章链接抓取、内容抓取和阅读量统计功能
"""

import os
from read_cookie import ReadCookie
from wxCrawler import WxCrawler
from batch_readnum_spider import BatchReadnumSpider

def show_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("🚀 微信公众号爬虫工具集")
    print("="*60)
    print("1. 抓取Cookie（首次使用或Cookie过期时）")
    print("2. 基础文章抓取（支持链接+内容获取）")
    print("3. 批量阅读量统计抓取（推荐）")
    print("4. 查看Cookie状态")
    print("5. 退出程序")
    print("="*60)

def extract_cookies():
    """抓取Cookie"""
    print("\n🔧 启动Cookie抓取器...")
    cookie_reader = ReadCookie()
    
    print("请选择操作:")
    print("1. 自动启动抓取器（需要手动访问微信公众号）")
    print("2. 只解析现有cookie文件")
    
    choice = input("请选择(1/2): ").strip()
    
    if choice == '1':
        # 启动抓取器
        if cookie_reader.start_cookie_extractor(timeout=120):
            print("\n抓取完成，开始解析...")
        else:
            print("抓取器启动失败")
            return False
    
    # 解析cookie
    result = cookie_reader.get_latest_cookies()
    
    if result:
        print("\n" + "="*50)
        print("✅ Cookie解析成功:")
        print(f"   __biz: {result['biz']}")
        print(f"   appmsg_token: {result['appmsg_token'][:20]}...")
        print(f"   解析时间: {result['timestamp']}")
        print("="*50)
        return True
    else:
        print("❌ Cookie解析失败，请确保:")
        print("1. 已正确访问微信公众号文章")
        print("2. 代理设置正确(127.0.0.1:8080)")
        print("3. wechat_keys.txt文件中有有效数据")
        return False

def basic_crawler():
    """基础文章链接抓取（增强版，支持保存到文件和获取文章内容）"""
    print("\n📖 启动基础文章链接抓取器...")

    # 检查cookie文件
    if not os.path.exists('wechat_keys.txt'):
        print("❌ 未找到cookie文件，请先抓取Cookie")
        return

    cookie_reader = ReadCookie('wechat_keys.txt')
    appmsg_token, biz, cookie_str = cookie_reader.parse_cookie()

    if not all([appmsg_token, biz, cookie_str]):
        print("❌ Cookie信息不完整，请重新抓取Cookie")
        return

    # 获取用户输入
    try:
        begin_page = int(input("起始页数 (默认0): ") or "0")
        end_page = int(input("结束页数 (默认5): ") or "5")
        save_to_file = input("是否保存到文件？(y/n，默认y): ").strip().lower() or "y"

        # 新增：询问是否获取文章内容
        print("\n📄 内容获取选项:")
        print("1. 仅获取文章链接信息（快速）")
        print("2. 同时获取文章完整内容（较慢，建议减少页数）")
        content_choice = input("请选择 (1/2，默认1): ").strip() or "1"
        get_content = content_choice == "2"

        if get_content and end_page > 2:
            confirm = input(f"⚠️ 获取内容模式建议页数不超过2页，当前设置{end_page}页，是否继续？(y/n): ").strip().lower()
            if confirm != 'y':
                end_page = int(input("请重新输入结束页数 (建议2): ") or "2")

    except ValueError:
        print("❌ 页数输入无效，使用默认值")
        begin_page, end_page = 0, 5
        save_to_file = "y"
        get_content = False

    # 根据是否获取内容设置数据目录
    if get_content:
        data_dir = "./data/with_content"
    else:
        data_dir = "./data/basic_links"
    os.makedirs(data_dir, exist_ok=True)

    # 启动增强版爬虫
    from enhanced_wx_crawler import EnhancedWxCrawler
    wx = EnhancedWxCrawler(
        appmsg_token,
        biz,
        cookie_str,
        begin_page,
        end_page,
        save_to_file == "y",
        get_content  # 传递内容获取参数
    )

    try:
        results = wx.run()
        if save_to_file == "y" and results:
            content_type = "文章内容" if get_content else "文章链接"
            print(f"\n✅ 抓取完成！共获取 {len(results)} 篇{content_type}")
            print(f"📁 数据已保存到: {data_dir}")

            # 显示抓取摘要
            wx.print_summary()
        elif results:
            content_type = "文章内容" if get_content else "文章链接"
            print(f"\n✅ 抓取完成！共获取 {len(results)} 篇{content_type}")
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断抓取")
        # 如果有数据且用户选择保存，尝试保存已抓取的数据
        if save_to_file == "y" and wx.articles_data:
            print("💾 保存已抓取的数据...")
            wx.save_data()
    except Exception as e:
        print(f"❌ 抓取过程出错: {e}")
        import traceback
        traceback.print_exc()

def batch_readnum_crawler():
    """批量阅读量统计抓取"""
    print("\n📊 启动批量阅读量统计抓取器...")
    
    # 检查cookie文件
    if not os.path.exists('wechat_keys.txt'):
        print("❌ 未找到cookie文件，请先抓取Cookie")
        return
    
    # 获取用户配置
    print("\n请配置抓取参数:")
    try:
        max_pages = int(input("最大页数 (默认3): ") or "3")
        articles_per_page = int(input("每页文章数 (默认10): ") or "10")
        days_back = int(input("抓取多少天内的文章 (默认7): ") or "7")
    except ValueError:
        print("❌ 参数输入无效，使用默认值")
        max_pages, articles_per_page, days_back = 3, 10, 7
    
    # 初始化爬虫
    spider = BatchReadnumSpider()
    
    try:
        print(f"\n🚀 开始批量抓取...")
        print(f"📋 配置: {max_pages}页 × {articles_per_page}篇/页，{days_back}天内文章")
        
        # 执行抓取
        results = spider.batch_crawl_readnum(
            max_pages=max_pages,
            articles_per_page=articles_per_page,
            days_back=days_back
        )
        
        if results:
            # 显示统计摘要
            spider.print_summary()
            
            # 保存数据
            print(f"\n💾 正在保存数据...")
            excel_file = spider.save_to_excel()
            json_file = spider.save_to_json()
            
            print(f"\n🎉 抓取完成！")
            if excel_file:
                print(f"📊 Excel文件: {excel_file}")
            if json_file:
                print(f"💾 JSON文件: {json_file}")
        else:
            print("❌ 未获取到任何数据，请检查Cookie是否有效")
    
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断抓取")
        if spider.articles_data:
            print("💾 保存已抓取的数据...")
            spider.save_to_excel()
            spider.save_to_json()
    except Exception as e:
        print(f"❌ 抓取过程出错: {e}")
        import traceback
        traceback.print_exc()

def check_cookie_status():
    """检查Cookie状态"""
    print("\n🔍 检查Cookie状态...")
    
    if not os.path.exists('wechat_keys.txt'):
        print("❌ 未找到cookie文件 (wechat_keys.txt)")
        return
    
    cookie_reader = ReadCookie('wechat_keys.txt')
    result = cookie_reader.get_latest_cookies()
    
    if result:
        print("✅ Cookie状态正常:")
        print(f"   __biz: {result['biz']}")
        print(f"   appmsg_token: {result['appmsg_token'][:20]}...")
        print(f"   cookie长度: {len(result['cookie_str'])} 字符")
        print(f"   解析时间: {result['timestamp']}")
    else:
        print("❌ Cookie无效或已过期，请重新抓取")

def main():
    """主程序入口"""
    print("🎯 微信公众号爬虫工具集 v2.1")
    print("支持文章链接抓取、完整内容获取和阅读量统计")
    
    while True:
        try:
            show_menu()
            choice = input("\n请选择功能 (1-5): ").strip()
            
            if choice == '1':
                extract_cookies()
            elif choice == '2':
                basic_crawler()
            elif choice == '3':
                batch_readnum_crawler()
            elif choice == '4':
                check_cookie_status()
            elif choice == '5':
                print("👋 感谢使用，再见！")
                break
            else:
                print("❌ 无效选择，请输入1-5")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，程序退出")
            break
        except Exception as e:
            print(f"❌ 程序出错: {e}")
            continue

if __name__ == '__main__':
    main()
